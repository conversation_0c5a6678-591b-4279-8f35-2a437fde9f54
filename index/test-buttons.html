<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Button Test - External JS Only</title>
    <style>
        .btn {
            padding: 10px 20px;
            margin: 10px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }
        .btn-yellow {
            background-color: #f7ef22;
            color: black;
        }
        .btn-outline {
            background-color: #f7941d;
            color: white;
        }
        .interact-button {
            font-weight: bold;
        }
        .external-js-only {
            border: 2px solid green !important;
        }
        .external-js-only::after {
            content: " ✓ External JS Only";
            color: green;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>Button Test - Before and After Cleanup</h1>
    
    <h2>Original Buttons (with problematic attributes):</h2>
    
    <!-- CONNECT Button -->
    <a class="btn btn-yellow text-black bg-[#f7ef22] w-5/12" data-savepage-href="/connect" href="https://btcbulltoken.ke/connect">
        <span class="interact-button">CONNECT</span>
    </a>
    
    <!-- CLAIM $BTCBULL Button -->
    <span class="interact-button">CLAIM $BTCBULL</span>
    
    <!-- Validate Button -->
    <a class="interact-button btn btn-outline uppercase text-white bg-[#f7941d] w-5/12" data-savepage-href="/connect" href="https://btcbulltoken.ke/connect">
        <span>Validate</span>
    </a>
    
    <!-- Stake/Unstake Button -->
    <span class="">Stake/Unstake</span>
    
    <h2>Instructions:</h2>
    <p>1. The buttons above should have their href attributes removed</p>
    <p>2. Buttons cleaned by the script will have a green border and checkmark</p>
    <p>3. External JavaScript can now safely attach event listeners without conflicts</p>
    
    <h2>Test External Event Listeners:</h2>
    <button onclick="testExternalListeners()">Test External Event Listeners</button>
    
    <div id="log"></div>
    
    <!-- Include the button cleanup script -->
    <script src="./fix-buttons.js"></script>
    
    <script>
        // Example external JavaScript that should work after cleanup
        function testExternalListeners() {
            const log = document.getElementById('log');
            log.innerHTML = '<h3>External Event Listener Test Results:</h3>';
            
            // Add external event listeners to cleaned buttons
            const connectBtn = document.querySelector('.interact-button');
            const claimBtn = document.querySelector('span:contains("CLAIM")');
            const validateBtn = document.querySelector('a .interact-button');
            const stakeBtn = document.querySelector('span:contains("Stake")');
            
            // Test CONNECT button
            if (connectBtn) {
                connectBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    log.innerHTML += '<p>✓ CONNECT button clicked via external listener</p>';
                });
                connectBtn.setAttribute('data-external-handler', 'true');
            }
            
            // Add listeners to all cleaned buttons
            const cleanedButtons = document.querySelectorAll('.external-js-only');
            cleanedButtons.forEach((btn, index) => {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    log.innerHTML += `<p>✓ Button ${index + 1} clicked via external listener: "${btn.textContent.trim()}"</p>`;
                });
                btn.setAttribute('data-external-handler', 'true');
            });
            
            log.innerHTML += '<p><strong>External event listeners attached! Click the buttons above to test.</strong></p>';
        }
        
        // Auto-run test after page loads
        window.addEventListener('load', function() {
            setTimeout(testExternalListeners, 1500);
        });
    </script>
</body>
</html>

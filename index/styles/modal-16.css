@import url('https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100..900;1,100..900&display=swap');



*, ::before, ::after{--tw-border-spacing-x:0;--tw-border-spacing-y:0;--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness:proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-color:rgb(59 130 246 / 0.5);--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: ;--tw-contain-size: ;--tw-contain-layout: ;--tw-contain-paint: ;--tw-contain-style: }::backdrop{--tw-border-spacing-x:0;--tw-border-spacing-y:0;--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness:proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-color:rgb(59 130 246 / 0.5);--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: ;--tw-contain-size: ;--tw-contain-layout: ;--tw-contain-paint: ;--tw-contain-style: } *,::after,::before{box-sizing:border-box;border-width:0;border-style:solid;border-color:#e5e7eb}::after,::before{--tw-content:''}:host,html{line-height:1.5;-webkit-text-size-adjust:100%;-moz-tab-size:4;tab-size:4;font-family:ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";font-feature-settings:normal;font-variation-settings:normal;-webkit-tap-highlight-color:transparent}body{margin:0;line-height:inherit}hr{height:0;color:inherit;border-top-width:1px}abbr:where([title]){-webkit-text-decoration:underline dotted;text-decoration:underline dotted}h1,h2,h3,h4,h5,h6{font-size:inherit;font-weight:inherit}a{color:inherit;text-decoration:inherit}b,strong{font-weight:bolder}code,kbd,pre,samp{font-family:ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;font-feature-settings:normal;font-variation-settings:normal;font-size:1em}small{font-size:80%}sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}sub{bottom:-.25em}sup{top:-.5em}table{text-indent:0;border-color:inherit;border-collapse:collapse}button,input,optgroup,select,textarea{font-family:inherit;font-feature-settings:inherit;font-variation-settings:inherit;font-size:100%;font-weight:inherit;line-height:inherit;letter-spacing:inherit;color:inherit;margin:0;padding:0}button,select{text-transform:none}button,input:where([type=button]),input:where([type=reset]),input:where([type=submit]){-webkit-appearance:button;background-color:transparent;background-image:none}:-moz-focusring{outline:auto}:-moz-ui-invalid{box-shadow:none}progress{vertical-align:baseline}::-webkit-inner-spin-button,::-webkit-outer-spin-button{height:auto}[type=search]{-webkit-appearance:textfield;outline-offset:-2px}::-webkit-search-decoration{-webkit-appearance:none}::-webkit-file-upload-button{-webkit-appearance:button;font:inherit}summary{display:list-item}blockquote,dd,dl,figure,h1,h2,h3,h4,h5,h6,hr,p,pre{margin:0}fieldset{margin:0;padding:0}legend{padding:0}menu,ol,ul{list-style:none;margin:0;padding:0}dialog{padding:0}textarea{resize:vertical}input::placeholder,textarea::placeholder{opacity:1;color:#9ca3af}[role=button],button{cursor:pointer}:disabled{cursor:default}audio,canvas,embed,iframe,img,object,svg,video{display:block;vertical-align:middle}img,video{max-width:100%;height:auto}[hidden]:where(:not([hidden=until-found])){display:none}.scwtw-pointer-events-none{pointer-events:none}.scwtw-fixed{position:fixed}.scwtw-bottom-0{bottom:0px}.scwtw-left-0{left:0px}.scwtw-right-0{right:0px}.scwtw-mx-auto{margin-left:auto;margin-right:auto}.scwtw-mt-4{margin-top:1rem}.scwtw-block{display:block}.scwtw-flex{display:flex}.scwtw-grid{display:grid}.scwtw-hidden{display:none}.scwtw-h-screen{height:100vh}.scwtw-h-5{height:1.25rem}.scwtw-h-\[1px\]{height:1px}.scwtw-h-\[40px\]{height:40px}.scwtw-h-auto{height:auto}.scwtw-max-h-\[72px\]{max-height:72px}.scwtw-max-h-\[calc\(100vh-70px\)\]{max-height:calc(100vh - 70px)}.scwtw-w-screen{width:100vw}.scwtw-w-5{width:1.25rem}.scwtw-w-\[40px\]{width:40px}.scwtw-w-full{width:100%}.scwtw-max-w-full{max-width:100%}.scwtw-translate-y-\[20px\]{--tw-translate-y:20px;transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.scwtw-translate-y-0{--tw-translate-y:0px;transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}@keyframes scwtw-spin{to{transform:rotate(360deg)}}.scwtw-animate-spin{animation:scwtw-spin 1s linear infinite}.scwtw-cursor-pointer{cursor:pointer}.scwtw-flex-col{flex-direction:column}.scwtw-items-center{align-items:center}.scwtw-justify-start{justify-content:flex-start}.scwtw-justify-end{justify-content:flex-end}.scwtw-justify-center{justify-content:center}.scwtw-justify-between{justify-content:space-between}.scwtw-gap-2{gap:0.5rem}.scwtw-gap-4{gap:1rem}.scwtw-gap-x-2{column-gap:0.5rem}.scwtw-gap-x-3{column-gap:0.75rem}.scwtw-overflow-y-auto{overflow-y:auto}.scwtw-whitespace-nowrap{white-space:nowrap}.scwtw-rounded-\[10px\]{border-radius:10px}.scwtw-rounded-\[16px\]{border-radius:16px}.scwtw-rounded-t-\[20px\]{border-top-left-radius:20px;border-top-right-radius:20px}.scwtw-border{border-width:1px}.scwtw-border-\[\#22222212\]{border-color:#22222212}.scwtw-bg-\[\#22222212\]{background-color:#22222212}.scwtw-bg-\[\#f9f9f9\]{--tw-bg-opacity:1;background-color:rgb(249 249 249 / var(--tw-bg-opacity, 1))}.scwtw-bg-white{--tw-bg-opacity:1;background-color:rgb(255 255 255 / var(--tw-bg-opacity, 1))}.scwtw-bg-gradient-to-r{background-image:linear-gradient(to right, var(--tw-gradient-stops))}.scwtw-from-\[\#fd82de\]{--tw-gradient-from:#fd82de var(--tw-gradient-from-position);--tw-gradient-to:rgb(253 130 222 / 0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to)}.scwtw-to-\[\#fd2c99\]{--tw-gradient-to:#fd2c99 var(--tw-gradient-to-position)}.scwtw-p-4{padding:1rem}.scwtw-px-4{padding-left:1rem;padding-right:1rem}.scwtw-py-\[20px\]{padding-top:20px;padding-bottom:20px}.scwtw-pb-2{padding-bottom:0.5rem}.scwtw-text-left{text-align:left}.scwtw-text-\[12px\]{font-size:12px}.scwtw-text-\[14px\]{font-size:14px}.scwtw-text-\[16px\]{font-size:16px}.scwtw-font-medium{font-weight:500}.scwtw-font-normal{font-weight:400}.-scwtw-tracking-\[0\.01em\]{letter-spacing:-0.01em}.scwtw-text-\[\#222222\]{--tw-text-opacity:1;color:rgb(34 34 34 / var(--tw-text-opacity, 1))}.scwtw-text-\[\#7d7d7d\]{--tw-text-opacity:1;color:rgb(125 125 125 / var(--tw-text-opacity, 1))}.scwtw-text-\[\#fc72ff\]{--tw-text-opacity:1;color:rgb(252 114 255 / var(--tw-text-opacity, 1))}.scwtw-text-white{--tw-text-opacity:1;color:rgb(255 255 255 / var(--tw-text-opacity, 1))}.scwtw-opacity-0{opacity:0}.scwtw-opacity-100{opacity:1}.scwtw-shadow-\[12px_16px_24px_rgba\(0\,0\,0\,0\.24\)\,12px_8px_12px_rgba\(0\,0\,0\,0\.24\)\,4px_4px_8px_rgba\(0\,0\,0\,0\.32\)\]{--tw-shadow:12px 16px 24px rgba(0,0,0,0.24),12px 8px 12px rgba(0,0,0,0.24),4px 4px 8px rgba(0,0,0,0.32);--tw-shadow-colored:12px 16px 24px var(--tw-shadow-color), 12px 8px 12px var(--tw-shadow-color), 4px 4px 8px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)}.scwtw-shadow-\[8px_12px_20px_rgba\(51\,53\,72\,0\.04\)\,4px_6px_12px_rgba\(51\,53\,72\,0\.02\)\,4px_4px_8px_rgba\(51\,53\,72\,0\.04\)\]{--tw-shadow:8px 12px 20px rgba(51,53,72,0.04),4px 6px 12px rgba(51,53,72,0.02),4px 4px 8px rgba(51,53,72,0.04);--tw-shadow-colored:8px 12px 20px var(--tw-shadow-color), 4px 6px 12px var(--tw-shadow-color), 4px 4px 8px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)}.scwtw-transition-all{transition-property:all;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);transition-duration:150ms}.scwtw-duration-200{transition-duration:200ms}.scwtw-duration-300{transition-duration:300ms}.scwtw-ease-out{transition-timing-function:cubic-bezier(0, 0, 0.2, 1)}.hover\:scwtw-bg-\[\#22222212\]:hover{background-color:#22222212}.hover\:scwtw-opacity-60:hover{opacity:0.6}.hover\:scwtw-opacity-80:hover{opacity:0.8}.disabled\:scwtw-cursor-not-allowed:disabled{cursor:not-allowed}.disabled\:scwtw-opacity-70:disabled{opacity:0.7}.data-\[is-loading\=true\]\:scwtw-flex[data-is-loading="true"]{display:flex}@media (min-width: 768px){.md\:scwtw-bottom-auto{bottom:auto}.md\:scwtw-left-auto{left:auto}.md\:scwtw-right-\[20px\]{right:20px}.md\:scwtw-top-\[50px\]{top:50px}.md\:scwtw-mx-0{margin-left:0px;margin-right:0px}.md\:scwtw-max-w-\[388px\]{max-width:388px}.md\:scwtw-rounded-\[20px\]{border-radius:20px}}.dark\:scwtw-block:where([data-theme="dark"], [data-theme="dark"] *){display:block}.dark\:scwtw-hidden:where([data-theme="dark"], [data-theme="dark"] *){display:none}.dark\:scwtw-border-\[\#ffffff12\]:where([data-theme="dark"], [data-theme="dark"] *){border-color:#ffffff12}.dark\:scwtw-bg-\[\#131313\]:where([data-theme="dark"], [data-theme="dark"] *){--tw-bg-opacity:1;background-color:rgb(19 19 19 / var(--tw-bg-opacity, 1))}.dark\:scwtw-bg-\[\#1b1b1b\]:where([data-theme="dark"], [data-theme="dark"] *){--tw-bg-opacity:1;background-color:rgb(27 27 27 / var(--tw-bg-opacity, 1))}.dark\:scwtw-bg-\[\#ffffff12\]:where([data-theme="dark"], [data-theme="dark"] *){background-color:#ffffff12}.dark\:scwtw-text-\[\#9b9b9b\]:where([data-theme="dark"], [data-theme="dark"] *){--tw-text-opacity:1;color:rgb(155 155 155 / var(--tw-text-opacity, 1))}.dark\:scwtw-text-white:where([data-theme="dark"], [data-theme="dark"] *){--tw-text-opacity:1;color:rgb(255 255 255 / var(--tw-text-opacity, 1))}.dark\:hover\:scwtw-bg-\[\#ffffff12\]:hover:where([data-theme="dark"], [data-theme="dark"] *){background-color:#ffffff12}


#connect-modal {
	z-index: 2147483646;
	font-family: 'Roboto', sans-serif;
	transition: .2s ease-in-out;
    scrollbar-width: thin;
    scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}

#connect-modal-overlay {
	height: 100%;
	z-index: 2147483645;
	position: fixed;
	top: 0;
	left: 0;
	width: 100%
}

#connect-modal::-webkit-scrollbar {
    width: 6px;
}

#connect-modal::-webkit-scrollbar-track {
    background: transparent;
}

#connect-modal::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
    border: 2px solid transparent;
}

[data-theme=dark] #connect-modal {
    scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
}

[data-theme=dark] #connect-modal::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.2);
}

#connect-modal::-webkit-scrollbar-thumb:hover {
    background-color: rgba(0, 0, 0, 0.3);
}

[data-theme=dark] #connect-modal::-webkit-scrollbar-thumb:hover {
    background-color: rgba(255, 255, 255, 0.3);
}

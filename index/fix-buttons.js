/**
 * <PERSON><PERSON>t to remove inline event handlers and href attributes from specific buttons
 * This ensures buttons only respond to external JavaScript event listeners
 */

(function() {
    'use strict';
    
    // Function to clean button attributes
    function cleanButton(button) {
        if (!button) return;
        
        // Remove href attribute to prevent navigation
        if (button.hasAttribute('href')) {
            button.removeAttribute('href');
            console.log('Removed href attribute from button:', button);
        }
        
        // Remove data-savepage-href attribute
        if (button.hasAttribute('data-savepage-href')) {
            button.removeAttribute('data-savepage-href');
            console.log('Removed data-savepage-href attribute from button:', button);
        }
        
        // Remove any onclick handlers
        if (button.hasAttribute('onclick')) {
            button.removeAttribute('onclick');
            console.log('Removed onclick attribute from button:', button);
        }
        
        // Clear any inline event handlers
        button.onclick = null;
        button.onmousedown = null;
        button.onmouseup = null;
        button.ontouchstart = null;
        button.ontouchend = null;
        
        // Prevent default behavior for any remaining events
        button.addEventListener('click', function(e) {
            // Only prevent default if no external listeners are attached
            // This allows external JavaScript to handle the events properly
            if (!button.hasAttribute('data-external-handler')) {
                e.preventDefault();
            }
        }, true);
        
        // Add a class to indicate this button has been processed
        button.classList.add('external-js-only');
    }
    
    // Function to find and clean target buttons
    function findAndCleanButtons() {
        // Find CONNECT button - specifically target the structure you provided
        const connectButtons = document.querySelectorAll('a[href*="connect"], a[data-savepage-href*="connect"], .interact-button');
        connectButtons.forEach(button => {
            const text = button.textContent || button.innerText || '';
            if (text.includes('CONNECT')) {
                cleanButton(button);
            }
        });

        // Find CLAIM $BTCBULL button - target spans with this text
        const claimButtons = document.querySelectorAll('span, button, a');
        claimButtons.forEach(button => {
            const text = button.textContent || button.innerText || '';
            if (text.includes('CLAIM') && text.includes('BTCBULL')) {
                cleanButton(button);
                // Also clean parent element if it's a clickable container
                if (button.parentElement && (button.parentElement.tagName === 'A' || button.parentElement.onclick)) {
                    cleanButton(button.parentElement);
                }
            }
        });

        // Find Validate button - specifically target the structure you provided
        const validateButtons = document.querySelectorAll('a[href*="connect"], a[data-savepage-href*="connect"], button, span');
        validateButtons.forEach(button => {
            const text = button.textContent || button.innerText || '';
            if (text.includes('Validate')) {
                cleanButton(button);
            }
        });

        // Find Stake/Unstake button - target spans with this text
        const stakeButtons = document.querySelectorAll('span, button, a');
        stakeButtons.forEach(button => {
            const text = button.textContent || button.innerText || '';
            if (text.includes('Stake') || text.includes('Unstake')) {
                cleanButton(button);
                // Also clean parent element if it's a clickable container
                if (button.parentElement && (button.parentElement.tagName === 'A' || button.parentElement.onclick)) {
                    cleanButton(button.parentElement);
                }
            }
        });

        // Target all elements with interact-button class
        const interactButtons = document.querySelectorAll('.interact-button');
        interactButtons.forEach(cleanButton);

        // Target specific button structures based on your examples
        const specificButtons = document.querySelectorAll('a.btn.btn-yellow, a.btn.btn-outline');
        specificButtons.forEach(cleanButton);
    }
    
    // Run immediately
    findAndCleanButtons();
    
    // Run when DOM is fully loaded
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', findAndCleanButtons);
    }
    
    // Use MutationObserver to catch dynamically added buttons
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList') {
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        // Check if the added node is a button we need to clean
                        const text = node.textContent || node.innerText || '';
                        if (text.includes('CONNECT') || 
                            (text.includes('CLAIM') && text.includes('BTCBULL')) ||
                            text.includes('Validate') ||
                            text.includes('Stake') ||
                            text.includes('Unstake') ||
                            node.classList.contains('interact-button')) {
                            cleanButton(node);
                        }
                        
                        // Also check child elements
                        const childButtons = node.querySelectorAll('.interact-button, a[href*="connect"]');
                        childButtons.forEach(cleanButton);
                    }
                });
            }
        });
    });
    
    // Start observing
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
    
    console.log('Button cleanup script loaded and monitoring for dynamic content');
})();
